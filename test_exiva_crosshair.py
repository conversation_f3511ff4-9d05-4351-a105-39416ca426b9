#!/usr/bin/env python3
"""
Test script to verify the Exiva-style crosshair system with 8-directional lines and distance ranges.
"""

import sys
import math
from pathlib import Path
from typing import List

# Add the current directory to the path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QPointF
from minimap_viewer import MinimapGraphicsView

def test_exiva_diagonal_angles():
    """Test the Exiva diagonal boundary angle calculations."""
    print("Testing Exiva diagonal boundary angle calculations...")

    # Create a test instance
    view = MinimapGraphicsView()

    # Get the angles
    angles = view.get_exiva_diagonal_angles()

    print(f"Number of angles: {len(angles)}")
    print("Diagonal boundary angles:")
    directions = ["NNE", "ENE", "ESE", "SSE", "SSW", "WSW", "WNW", "NNW"]

    for i, (direction, angle) in enumerate(zip(directions, angles)):
        print(f"  {direction}: {angle:.2f}°")

    # Verify we have 8 angles
    assert len(angles) == 8, f"Expected 8 angles, got {len(angles)}"

    # Calculate expected angle from 2.42:1 ratio
    import math
    expected_primary = math.degrees(math.atan(1.0 / 2.42))
    expected_angles = [
        expected_primary,                    # NNE
        90.0 - expected_primary,            # ENE
        90.0 + expected_primary,            # ESE
        180.0 - expected_primary,           # SSE
        180.0 + expected_primary,           # SSW
        270.0 - expected_primary,           # WSW
        270.0 + expected_primary,           # WNW
        360.0 - expected_primary            # NNW
    ]

    for i, (expected, actual) in enumerate(zip(expected_angles, angles)):
        assert abs(expected - actual) < 0.1, f"Direction {directions[i]}: expected {expected:.2f}°, got {actual:.2f}°"

    print(f"✓ All diagonal boundary angles are correct! (Primary angle: {expected_primary:.2f}°)")

def test_diagonal_line_length_calculation():
    """Test the diagonal line length calculation."""
    print("\nTesting diagonal line length calculation...")
    
    view = MinimapGraphicsView()
    
    # Test with different zoom factors
    zoom_factors = [0.1, 0.5, 1.0, 2.0, 5.0]
    
    for zoom in zoom_factors:
        view.zoom_factor = zoom
        length = view.calculate_diagonal_line_length()
        print(f"  Zoom {zoom}x: Line length = {length:.1f}")
        
        # Verify length is reasonable
        assert 20.0 <= length <= 1000.0, f"Line length {length} is outside reasonable bounds"
    
    print("✓ Diagonal line length calculations are working!")

def test_exiva_range_calculations():
    """Test the Exiva range size calculations."""
    print("\nTesting Exiva range size calculations...")
    
    view = MinimapGraphicsView()
    
    # Test inner range (100 squares)
    inner_size = view.calculate_exiva_range_size(100)
    expected_inner = 201.0  # (100 * 2) + 1 to include center pixel
    assert abs(inner_size - expected_inner) < 0.1, f"Inner range: expected {expected_inner}, got {inner_size}"
    print(f"  Inner range (100 squares): {inner_size} pixels")

    # Test outer range (250 squares)
    outer_size = view.calculate_exiva_range_size(250)
    expected_outer = 501.0  # (250 * 2) + 1 to include center pixel
    assert abs(outer_size - expected_outer) < 0.1, f"Outer range: expected {expected_outer}, got {outer_size}"
    print(f"  Outer range (250 squares): {outer_size} pixels")
    
    print("✓ Exiva range calculations are correct!")

def test_crosshair_storage():
    """Test the crosshair diagonal line storage."""
    print("\nTesting crosshair diagonal line storage...")

    view = MinimapGraphicsView()

    # Verify we have 8 diagonal line slots
    assert len(view.crosshair_diagonals) == 8, f"Expected 8 diagonal slots, got {len(view.crosshair_diagonals)}"

    # Verify all slots are initially None
    for i, line in enumerate(view.crosshair_diagonals):
        assert line is None, f"Diagonal line {i} should be None initially"

    # Verify we have range overlay slots
    assert view.crosshair_inner_range is None, "Inner range should be None initially"
    assert view.crosshair_outer_range is None, "Outer range should be None initially"

    # Verify we still have center square
    assert view.crosshair_center_square is None, "Center square should be None initially"

    print("✓ Crosshair storage is properly initialized!")
    print("✓ 8-directional Exiva system with range overlays!")

def test_pen_width_scaling():
    """Test the pen width scaling calculations."""
    print("\nTesting pen width scaling...")

    view = MinimapGraphicsView()

    # Test with different zoom factors
    zoom_factors = [0.1, 0.5, 1.0, 2.0, 5.0]
    base_width = 0.3

    for zoom in zoom_factors:
        view.zoom_factor = zoom
        width = view.calculate_crosshair_pen_width(base_width)
        print(f"  Zoom {zoom}x: Pen width = {width:.3f}")

        # Verify width is reasonable
        assert 0.1 <= width <= 10.0, f"Pen width {width} is outside reasonable bounds"

    print("✓ Pen width scaling is working!")

def test_exiva_directional_sectors():
    """Test that the diagonal boundaries create proper directional sectors."""
    print("\nTesting Exiva directional sector boundaries...")

    import math

    # Calculate the primary angle from 2.42:1 ratio
    primary_angle_rad = math.atan(1.0 / 2.42)
    primary_angle_deg = math.degrees(primary_angle_rad)

    print(f"Primary diagonal angle: {primary_angle_deg:.2f}°")

    # Verify the 8 sectors are properly defined
    sectors = [
        ("North", 360.0 - primary_angle_deg, primary_angle_deg),
        ("Northeast", primary_angle_deg, 90.0 - primary_angle_deg),
        ("East", 90.0 - primary_angle_deg, 90.0 + primary_angle_deg),
        ("Southeast", 90.0 + primary_angle_deg, 180.0 - primary_angle_deg),
        ("South", 180.0 - primary_angle_deg, 180.0 + primary_angle_deg),
        ("Southwest", 180.0 + primary_angle_deg, 270.0 - primary_angle_deg),
        ("West", 270.0 - primary_angle_deg, 270.0 + primary_angle_deg),
        ("Northwest", 270.0 + primary_angle_deg, 360.0 - primary_angle_deg)
    ]

    total_coverage = 0
    for direction, start_angle, end_angle in sectors:
        if end_angle < start_angle:  # Handle wrap-around for North sector
            sector_size = (360.0 - start_angle) + end_angle
        else:
            sector_size = end_angle - start_angle
        total_coverage += sector_size
        print(f"  {direction}: {start_angle:.2f}° to {end_angle:.2f}° (size: {sector_size:.2f}°)")

    # Verify total coverage is 360 degrees
    assert abs(total_coverage - 360.0) < 0.1, f"Total sector coverage should be 360°, got {total_coverage:.2f}°"

    print(f"✓ All 8 directional sectors properly defined! Total coverage: {total_coverage:.2f}°")

def main():
    """Run all tests."""
    print("Testing Exiva-Style Crosshair System")
    print("=" * 60)

    # Create QApplication for Qt widgets
    app = QApplication(sys.argv)

    try:
        test_exiva_diagonal_angles()
        test_diagonal_line_length_calculation()
        test_exiva_range_calculations()
        test_crosshair_storage()
        test_pen_width_scaling()
        test_exiva_directional_sectors()
        
        print("\n" + "=" * 60)
        print("🎉 All tests passed! The Exiva crosshair system is working correctly.")
        print("\nFeatures implemented:")
        print("✓ 8 diagonal boundary lines (NNE, ENE, ESE, SSE, SSW, WSW, WNW, NNW)")
        print("✓ Tibia-accurate 2.42:1 diagonal ratio")
        print("✓ Proper directional sectors with asymmetrical boundaries")
        print("✓ Dynamic line length based on zoom level")
        print("✓ Exiva distance range overlays (100 & 250 square ranges)")
        print("✓ Tibia-style colors (gold lines, green/orange ranges, white center)")
        print("✓ Dynamic pen width scaling for all zoom levels")
        print("✓ Proper storage and management of all crosshair elements")
        
        print("\nTo test visually:")
        print("1. Run: python main.py")
        print("2. Right-click on the minimap to place Exiva crosshairs")
        print("3. You should see:")
        print("   - 8 gold diagonal boundary lines creating directional sectors")
        print("   - Lines use the correct 2.42:1 slope ratio")
        print("   - Green rectangle showing 100-square range")
        print("   - Orange rectangle showing 250-square range")
        print("   - White center square for precise positioning")
        print("4. Zoom in/out to see all elements adjust their appearance")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
